import React from 'react';
import { X } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { modal } from '@/lib/responsive';

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | 'full';
  className?: string;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
}

const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  size = 'lg',
  className,
  showCloseButton = true,
  closeOnOverlayClick = true,
}) => {
  const { isRTL } = useLanguage();
  const isMobile = useIsMobile();

  const getSizeClasses = () => {
    const sizeConfig = modal.sizes[size as keyof typeof modal.sizes];
    if (sizeConfig) {
      return isMobile ? sizeConfig.mobile : sizeConfig.sm;
    }

    // Enhanced fallback for custom sizes with better responsive handling
    const fallbackSizes = {
      sm: 'max-w-[calc(100vw-1rem)] sm:max-w-sm',
      md: 'max-w-[calc(100vw-1rem)] sm:max-w-md',
      lg: 'max-w-[calc(100vw-1rem)] sm:max-w-lg md:max-w-2xl',
      xl: 'max-w-[calc(100vw-1rem)] sm:max-w-xl md:max-w-3xl lg:max-w-4xl',
      '2xl': 'max-w-[calc(100vw-1rem)] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl',
      '3xl': 'max-w-[calc(100vw-1rem)] sm:max-w-3xl md:max-w-5xl lg:max-w-7xl',
      '4xl': 'max-w-[calc(100vw-1rem)] sm:max-w-4xl md:max-w-6xl lg:max-w-[90rem]',
      full: 'max-w-[calc(100vw-1rem)] sm:max-w-[95vw]',
    };

    return fallbackSizes[size as keyof typeof fallbackSizes] || fallbackSizes.lg;
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalRef = React.useRef<HTMLDivElement>(null);

  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }

    // Trap focus within modal
    if (e.key === 'Tab' && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    }
  }, [onClose]);

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';

      // Focus the modal when it opens
      setTimeout(() => {
        const firstFocusable = modalRef.current?.querySelector(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        ) as HTMLElement;
        firstFocusable?.focus();
      }, 100);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50",
        // Responsive padding with safe area support
        "p-2 sm:p-4 md:p-6",
        // Support for devices with notches/safe areas
        "supports-[padding:max(0px)]:p-[max(0.5rem,env(safe-area-inset-top))]",
        "sm:supports-[padding:max(0px)]:p-[max(1rem,env(safe-area-inset-top))]"
      )}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby={description ? "modal-description" : undefined}
    >
      <div
        ref={modalRef}
        className={cn(
          'bg-background border border-border shadow-xl w-full',
          'transform transition-all duration-200 ease-out',
          'animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2',
          'flex flex-col',
          // Responsive border radius
          'rounded-lg sm:rounded-xl',
          // Enhanced height management with better constraints
          isMobile
            ? 'max-h-[calc(100vh-1rem)] min-h-[50vh]'
            : 'max-h-[calc(100vh-3rem)] min-h-[40vh]',
          // Better width constraints
          'max-w-[calc(100vw-1rem)] sm:max-w-[calc(100vw-2rem)]',
          getSizeClasses(),
          className
        )}
      >
        {/* Header - Fixed */}
        <div className={cn(
          'flex items-center justify-between border-b border-border bg-card/50 flex-shrink-0',
          // Responsive padding
          'p-3 sm:p-4 md:p-6',
          isRTL && 'flex-row-reverse'
        )}>
          <div className="flex-1 min-w-0">
            <h2
              id="modal-title"
              className={cn(
                "font-semibold text-foreground truncate",
                // Responsive text sizing
                "text-base sm:text-lg md:text-xl"
              )}
            >
              {title}
            </h2>
            {description && (
              <p
                id="modal-description"
                className={cn(
                  "text-muted-foreground mt-1 line-clamp-2",
                  // Responsive text sizing
                  "text-xs sm:text-sm"
                )}
              >
                {description}
              </p>
            )}
          </div>
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={cn(
                'flex-shrink-0 hover:bg-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
                // Responsive sizing and spacing
                'h-7 w-7 p-0 sm:h-8 sm:w-8',
                // Better touch targets on mobile
                'touch-manipulation',
                isRTL ? 'ml-3 sm:ml-4' : 'mr-0'
              )}
            >
              <X className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="sr-only">Close</span>
            </Button>
          )}
        </div>

        {/* Content - Scrollable */}
        <div className={cn(
          "flex-1 overflow-y-auto overflow-x-hidden modal-content",
          // Enhanced scrollbar styling
          "scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border",
          // Responsive padding
          "p-3 sm:p-4 md:p-6"
        )}>
          <div className="min-h-0">
            {children}
          </div>
        </div>

        {/* Footer - Fixed */}
        {footer && (
          <div className={cn(
            'border-t border-border bg-card/30 flex-shrink-0',
            // Responsive padding
            'p-3 sm:p-4 md:p-6',
            isRTL && 'flex-row-reverse'
          )}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponsiveModal;
