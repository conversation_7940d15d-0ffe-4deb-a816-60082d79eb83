/**
 * Enhanced Alert Dialog Component for Zenith Pulse Manager
 * 
 * An improved version of the shadcn-ui AlertDialog component with better responsive behavior,
 * accessibility features, and cross-device compatibility.
 */

import * as React from "react"
import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { 
  getModalOverlayClasses,
  getModalContentClasses,
  getModalHeaderClasses,
  getModalFooterClasses,
  getModalA11yProps,
  trapFocus,
  lockBodyScroll,
  unlockBodyScroll,
  modalSizes
} from "@/lib/modal-utils"

const EnhancedAlertDialog = AlertDialogPrimitive.Root

const EnhancedAlertDialogTrigger = AlertDialogPrimitive.Trigger

const EnhancedAlertDialogPortal = AlertDialogPrimitive.Portal

const EnhancedAlertDialogOverlay = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Overlay
    className={cn(
      "fixed inset-0 z-50 bg-black/80 backdrop-blur-sm",
      "data-[state=open]:animate-in data-[state=closed]:animate-out",
      "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
    ref={ref}
  />
))
EnhancedAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName

interface EnhancedAlertDialogContentProps 
  extends React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content> {
  size?: keyof typeof modalSizes
}

const EnhancedAlertDialogContent = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Content>,
  EnhancedAlertDialogContentProps
>(({ className, size = 'sm', ...props }, ref) => {
  const contentRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const content = contentRef.current
    if (!content) return

    lockBodyScroll()
    const cleanup = trapFocus(content)

    return () => {
      unlockBodyScroll()
      cleanup()
    }
  }, [])

  return (
    <EnhancedAlertDialogPortal>
      <EnhancedAlertDialogOverlay />
      <AlertDialogPrimitive.Content
        ref={ref}
        className={getModalContentClasses(size, 'md', className)}
        {...props}
        {...getModalA11yProps('Alert Dialog')}
      >
        <div ref={contentRef} className="flex flex-col h-full">
          {props.children}
        </div>
      </AlertDialogPrimitive.Content>
    </EnhancedAlertDialogPortal>
  )
})
EnhancedAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName

const EnhancedAlertDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={getModalHeaderClasses(cn(
      "flex flex-col space-y-2 text-center sm:text-left",
      className
    ))}
    {...props}
  />
)
EnhancedAlertDialogHeader.displayName = "EnhancedAlertDialogHeader"

const EnhancedAlertDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={getModalFooterClasses(className)}
    {...props}
  />
)
EnhancedAlertDialogFooter.displayName = "EnhancedAlertDialogFooter"

const EnhancedAlertDialogTitle = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Title
    ref={ref}
    id="modal-title"
    className={cn(
      "font-semibold leading-none tracking-tight",
      "text-base sm:text-lg md:text-xl",
      className
    )}
    {...props}
  />
))
EnhancedAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName

const EnhancedAlertDialogDescription = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Description
    ref={ref}
    id="modal-description"
    className={cn(
      "text-muted-foreground",
      "text-xs sm:text-sm",
      className
    )}
    {...props}
  />
))
EnhancedAlertDialogDescription.displayName = AlertDialogPrimitive.Description.displayName

const EnhancedAlertDialogAction = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Action>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Action
    ref={ref}
    className={cn(
      buttonVariants(),
      "touch-manipulation",
      className
    )}
    {...props}
  />
))
EnhancedAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName

const EnhancedAlertDialogCancel = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Cancel
    ref={ref}
    className={cn(
      buttonVariants({ variant: "outline" }),
      "touch-manipulation",
      className
    )}
    {...props}
  />
))
EnhancedAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName

// Convenience component for common alert dialog patterns
interface ResponsiveAlertDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
  size?: keyof typeof modalSizes
  className?: string
}

const ResponsiveAlertDialog: React.FC<ResponsiveAlertDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  size = 'sm',
  className
}) => {
  return (
    <EnhancedAlertDialog open={isOpen} onOpenChange={onClose}>
      <EnhancedAlertDialogContent size={size} className={className}>
        <EnhancedAlertDialogHeader>
          <EnhancedAlertDialogTitle>{title}</EnhancedAlertDialogTitle>
          {description && (
            <EnhancedAlertDialogDescription>{description}</EnhancedAlertDialogDescription>
          )}
        </EnhancedAlertDialogHeader>
        
        <EnhancedAlertDialogFooter>
          <EnhancedAlertDialogCancel onClick={onClose}>
            {cancelText}
          </EnhancedAlertDialogCancel>
          <EnhancedAlertDialogAction 
            onClick={onConfirm}
            className={variant === 'destructive' ? buttonVariants({ variant: 'destructive' }) : undefined}
          >
            {confirmText}
          </EnhancedAlertDialogAction>
        </EnhancedAlertDialogFooter>
      </EnhancedAlertDialogContent>
    </EnhancedAlertDialog>
  )
}

export {
  EnhancedAlertDialog,
  EnhancedAlertDialogPortal,
  EnhancedAlertDialogOverlay,
  EnhancedAlertDialogTrigger,
  EnhancedAlertDialogContent,
  EnhancedAlertDialogHeader,
  EnhancedAlertDialogFooter,
  EnhancedAlertDialogTitle,
  EnhancedAlertDialogDescription,
  EnhancedAlertDialogAction,
  EnhancedAlertDialogCancel,
  ResponsiveAlertDialog,
}
