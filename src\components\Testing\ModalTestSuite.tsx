/**
 * Modal Test Suite Component
 * 
 * A comprehensive testing component for validating modal/dialog improvements
 * across different screen sizes and use cases.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ResponsiveDialog } from '@/components/ui/enhanced-dialog';
import { ResponsiveAlertDialog } from '@/components/ui/enhanced-alert-dialog';
import { ResponsiveModal } from '@/components/Layout/ResponsiveModal';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { modalSizes } from '@/lib/modal-utils';
import { zIndexValidation } from '@/lib/z-index';

const ModalTestSuite: React.FC = () => {
  const { t, isRTL } = useLanguage();
  
  // State for different modal types
  const [dialogStates, setDialogStates] = useState<Record<string, boolean>>({});
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [responsiveModalOpen, setResponsiveModalOpen] = useState(false);
  const [currentSize, setCurrentSize] = useState<keyof typeof modalSizes>('md');

  // Helper to toggle dialog state
  const toggleDialog = (key: string) => {
    setDialogStates(prev => ({ ...prev, [key]: !prev[key] }));
  };

  // Test content for modals
  const generateTestContent = (length: 'short' | 'medium' | 'long') => {
    const shortContent = "This is a short content example for testing modal display.";
    const mediumContent = shortContent + " ".repeat(10) + shortContent + " This content is designed to test how modals handle medium-length text content across different screen sizes.";
    const longContent = Array(10).fill(mediumContent).join(" ");

    switch (length) {
      case 'short': return shortContent;
      case 'medium': return mediumContent;
      case 'long': return longContent;
      default: return shortContent;
    }
  };

  // Run z-index validation
  const runZIndexValidation = () => {
    const conflicts = zIndexValidation.checkConflicts();
    const isValid = zIndexValidation.validateModalOrder();
    
    alert(`Z-Index Validation:\nValid: ${isValid}\nConflicts: ${conflicts.length}\n\nCheck console for detailed report.`);
    console.log(zIndexValidation.generateReport());
  };

  return (
    <div className="p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Modal Test Suite</h1>
        <p className="text-muted-foreground">
          Comprehensive testing for modal/dialog improvements across all device types
        </p>
      </div>

      {/* Size Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Modal Size Testing</CardTitle>
          <CardDescription>
            Test different modal sizes across various screen sizes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 mb-4">
            {Object.keys(modalSizes).map((size) => (
              <Button
                key={size}
                variant={currentSize === size ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentSize(size as keyof typeof modalSizes)}
              >
                {size}
              </Button>
            ))}
          </div>
          <Button onClick={() => toggleDialog('sizeTest')}>
            Open {currentSize} Modal
          </Button>
        </CardContent>
      </Card>

      {/* Enhanced Dialog Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Dialog Tests</CardTitle>
          <CardDescription>
            Test the new enhanced dialog components with improved accessibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button onClick={() => toggleDialog('shortContent')}>
              Short Content Dialog
            </Button>
            <Button onClick={() => toggleDialog('mediumContent')}>
              Medium Content Dialog
            </Button>
            <Button onClick={() => toggleDialog('longContent')}>
              Long Content Dialog
            </Button>
            <Button onClick={() => toggleDialog('formDialog')}>
              Form Dialog
            </Button>
            <Button onClick={() => toggleDialog('nestedDialog')}>
              Nested Dialog Test
            </Button>
            <Button onClick={() => toggleDialog('noCloseButton')}>
              No Close Button
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alert Dialog Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Alert Dialog Tests</CardTitle>
          <CardDescription>
            Test confirmation dialogs and alert patterns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button onClick={() => setAlertDialogOpen(true)}>
              Confirmation Dialog
            </Button>
            <Button 
              variant="destructive"
              onClick={() => setAlertDialogOpen(true)}
            >
              Destructive Action
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Responsive Modal Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Responsive Modal Tests</CardTitle>
          <CardDescription>
            Test the existing ResponsiveModal component with improvements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setResponsiveModalOpen(true)}>
            Open Responsive Modal
          </Button>
        </CardContent>
      </Card>

      {/* Accessibility Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Accessibility & Z-Index Tests</CardTitle>
          <CardDescription>
            Test accessibility features and z-index management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button onClick={runZIndexValidation}>
              Validate Z-Index
            </Button>
            <Button onClick={() => toggleDialog('focusTest')}>
              Focus Management Test
            </Button>
            <Button onClick={() => toggleDialog('keyboardTest')}>
              Keyboard Navigation Test
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Modal Components */}
      
      {/* Size Test Dialog */}
      <ResponsiveDialog
        isOpen={dialogStates.sizeTest || false}
        onClose={() => toggleDialog('sizeTest')}
        title={`${currentSize.toUpperCase()} Size Modal`}
        description={`Testing ${currentSize} size modal across different screen sizes`}
        size={currentSize}
      >
        <div className="space-y-4">
          <p>This modal is using the <strong>{currentSize}</strong> size configuration.</p>
          <p>Resize your browser window to see how it adapts to different screen sizes.</p>
          <div className="bg-muted p-4 rounded">
            <h4 className="font-semibold mb-2">Size Details:</h4>
            <ul className="text-sm space-y-1">
              <li>Mobile: Full width with margins</li>
              <li>Tablet: Moderate constraints</li>
              <li>Desktop: Optimal reading width</li>
            </ul>
          </div>
        </div>
      </ResponsiveDialog>

      {/* Content Length Tests */}
      <ResponsiveDialog
        isOpen={dialogStates.shortContent || false}
        onClose={() => toggleDialog('shortContent')}
        title="Short Content Test"
        description="Testing modal with minimal content"
      >
        <p>{generateTestContent('short')}</p>
      </ResponsiveDialog>

      <ResponsiveDialog
        isOpen={dialogStates.mediumContent || false}
        onClose={() => toggleDialog('mediumContent')}
        title="Medium Content Test"
        description="Testing modal with moderate content length"
      >
        <p>{generateTestContent('medium')}</p>
      </ResponsiveDialog>

      <ResponsiveDialog
        isOpen={dialogStates.longContent || false}
        onClose={() => toggleDialog('longContent')}
        title="Long Content Test"
        description="Testing modal with extensive content that requires scrolling"
        size="lg"
      >
        <div className="space-y-4">
          <p>{generateTestContent('long')}</p>
          <div className="bg-muted p-4 rounded">
            <p>This content should be scrollable on smaller screens while maintaining proper modal constraints.</p>
          </div>
        </div>
      </ResponsiveDialog>

      {/* Alert Dialog */}
      <ResponsiveAlertDialog
        isOpen={alertDialogOpen}
        onClose={() => setAlertDialogOpen(false)}
        onConfirm={() => {
          alert('Action confirmed!');
          setAlertDialogOpen(false);
        }}
        title="Are you absolutely sure?"
        description="This action cannot be undone. This will permanently delete your account and remove your data from our servers."
        confirmText="Yes, delete account"
        cancelText="Cancel"
        variant="destructive"
      />

      {/* Responsive Modal */}
      <ResponsiveModal
        isOpen={responsiveModalOpen}
        onClose={() => setResponsiveModalOpen(false)}
        title="Responsive Modal Test"
        description="Testing the improved ResponsiveModal component"
        size="lg"
        footer={
          <div className={cn(
            'flex gap-3',
            isRTL ? 'flex-row-reverse' : 'flex-row'
          )}>
            <Button variant="outline" onClick={() => setResponsiveModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setResponsiveModalOpen(false)}>
              Confirm
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p>This modal uses the improved ResponsiveModal component with:</p>
          <ul className="list-disc list-inside space-y-2 text-sm">
            <li>Enhanced responsive sizing</li>
            <li>Better safe area support</li>
            <li>Improved accessibility features</li>
            <li>Optimized touch targets</li>
            <li>Consistent spacing and typography</li>
          </ul>
        </div>
      </ResponsiveModal>
    </div>
  );
};

export default ModalTestSuite;
