/**
 * Modal Utilities for Zenith Pulse Manager
 * 
 * Comprehensive utilities for managing modal/dialog components across different devices
 * and screen sizes. Follows PROJECT_STANDARDS.md guidelines and uses OKLCH color system.
 */

import { cn } from '@/lib/utils';

// Modal size configurations with enhanced responsive support
export const modalSizes = {
  xs: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-xs',
    desktop: 'max-w-xs',
  },
  sm: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-sm',
    desktop: 'max-w-sm',
  },
  md: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-md',
    desktop: 'max-w-md',
  },
  lg: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-lg',
    desktop: 'max-w-2xl',
  },
  xl: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-xl',
    desktop: 'max-w-4xl',
  },
  '2xl': {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-2xl',
    desktop: 'max-w-6xl',
  },
  '3xl': {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-3xl',
    desktop: 'max-w-7xl',
  },
  full: {
    mobile: 'max-w-[calc(100vw-1rem)]',
    tablet: 'max-w-[95vw]',
    desktop: 'max-w-[95vw]',
  },
} as const;

// Device breakpoints
export const deviceBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
} as const;

// Z-index management for layered modals
export const modalZIndex = {
  base: 50,
  overlay: 50,
  content: 51,
  toast: 100,
  tooltip: 200,
} as const;

// Modal positioning utilities
export const modalPositioning = {
  center: 'fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]',
  topCenter: 'fixed left-[50%] top-[10%] translate-x-[-50%]',
  bottomCenter: 'fixed left-[50%] bottom-[10%] translate-x-[-50%]',
} as const;

// Responsive padding configurations
export const modalPadding = {
  xs: 'p-2 sm:p-3',
  sm: 'p-3 sm:p-4',
  md: 'p-4 sm:p-6',
  lg: 'p-6 sm:p-8',
  xl: 'p-8 sm:p-10',
} as const;

// Animation configurations
export const modalAnimations = {
  fade: {
    enter: 'data-[state=open]:animate-in data-[state=open]:fade-in-0',
    exit: 'data-[state=closed]:animate-out data-[state=closed]:fade-out-0',
  },
  scale: {
    enter: 'data-[state=open]:zoom-in-95',
    exit: 'data-[state=closed]:zoom-out-95',
  },
  slide: {
    fromTop: 'data-[state=open]:slide-in-from-top-2',
    fromBottom: 'data-[state=open]:slide-in-from-bottom-2',
    fromLeft: 'data-[state=open]:slide-in-from-left-2',
    fromRight: 'data-[state=open]:slide-in-from-right-2',
    toTop: 'data-[state=closed]:slide-out-to-top-2',
    toBottom: 'data-[state=closed]:slide-out-to-bottom-2',
    toLeft: 'data-[state=closed]:slide-out-to-left-2',
    toRight: 'data-[state=closed]:slide-out-to-right-2',
  },
} as const;

// Device detection utilities
export const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  if (typeof window === 'undefined') return 'desktop';
  
  const width = window.innerWidth;
  if (width < deviceBreakpoints.mobile) return 'mobile';
  if (width < deviceBreakpoints.tablet) return 'tablet';
  return 'desktop';
};

// Get responsive modal size classes
export const getModalSizeClasses = (
  size: keyof typeof modalSizes,
  deviceType?: 'mobile' | 'tablet' | 'desktop'
): string => {
  const device = deviceType || getDeviceType();
  const sizeConfig = modalSizes[size];
  
  if (!sizeConfig) return modalSizes.md[device];
  
  return cn(
    sizeConfig.mobile,
    `sm:${sizeConfig.tablet}`,
    `lg:${sizeConfig.desktop}`
  );
};

// Generate modal overlay classes
export const getModalOverlayClasses = (className?: string): string => {
  return cn(
    'fixed inset-0 bg-black/50 backdrop-blur-sm',
    'flex items-center justify-center',
    `z-${modalZIndex.overlay}`,
    // Safe area support for mobile devices
    'p-2 sm:p-4 md:p-6',
    'supports-[padding:max(0px)]:p-[max(0.5rem,env(safe-area-inset-top))]',
    'sm:supports-[padding:max(0px)]:p-[max(1rem,env(safe-area-inset-top))]',
    className
  );
};

// Generate modal content classes
export const getModalContentClasses = (
  size: keyof typeof modalSizes = 'md',
  padding: keyof typeof modalPadding = 'md',
  className?: string
): string => {
  return cn(
    // Base positioning and layout
    'relative bg-background border border-border shadow-xl',
    'flex flex-col',
    // Responsive sizing
    getModalSizeClasses(size),
    // Height constraints
    'max-h-[calc(100vh-1rem)] sm:max-h-[calc(100vh-3rem)]',
    // Responsive border radius
    'rounded-lg sm:rounded-xl',
    // Z-index
    `z-${modalZIndex.content}`,
    // Padding
    modalPadding[padding],
    // Animations
    'transform transition-all duration-200 ease-out',
    modalAnimations.fade.enter,
    modalAnimations.fade.exit,
    modalAnimations.scale.enter,
    modalAnimations.scale.exit,
    modalAnimations.slide.fromBottom,
    modalAnimations.slide.toBottom,
    // Overflow handling
    'overflow-hidden',
    className
  );
};

// Generate modal header classes
export const getModalHeaderClasses = (className?: string): string => {
  return cn(
    'flex items-center justify-between',
    'border-b border-border bg-card/50 flex-shrink-0',
    'p-3 sm:p-4 md:p-6',
    className
  );
};

// Generate modal body classes
export const getModalBodyClasses = (className?: string): string => {
  return cn(
    'flex-1 overflow-y-auto overflow-x-hidden',
    'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border',
    'p-3 sm:p-4 md:p-6',
    className
  );
};

// Generate modal footer classes
export const getModalFooterClasses = (className?: string): string => {
  return cn(
    'border-t border-border bg-card/30 flex-shrink-0',
    'p-3 sm:p-4 md:p-6',
    // Responsive button layout
    'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3',
    '[&>button]:w-full sm:[&>button]:w-auto',
    className
  );
};

// Accessibility utilities
export const getModalA11yProps = (
  title: string,
  description?: string
) => ({
  role: 'dialog',
  'aria-modal': true,
  'aria-labelledby': 'modal-title',
  'aria-describedby': description ? 'modal-description' : undefined,
});

// Focus management utilities
export const trapFocus = (container: HTMLElement) => {
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  const firstElement = focusableElements[0] as HTMLElement;
  const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return;

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement?.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement?.focus();
        e.preventDefault();
      }
    }
  };

  container.addEventListener('keydown', handleTabKey);
  
  // Focus first element
  setTimeout(() => firstElement?.focus(), 100);

  return () => container.removeEventListener('keydown', handleTabKey);
};

// Body scroll lock utilities
export const lockBodyScroll = () => {
  document.body.style.overflow = 'hidden';
};

export const unlockBodyScroll = () => {
  document.body.style.overflow = 'unset';
};

// Viewport utilities
export const getViewportDimensions = () => ({
  width: window.innerWidth,
  height: window.innerHeight,
  isMobile: window.innerWidth < deviceBreakpoints.mobile,
  isTablet: window.innerWidth >= deviceBreakpoints.mobile && window.innerWidth < deviceBreakpoints.tablet,
  isDesktop: window.innerWidth >= deviceBreakpoints.tablet,
});
