/**
 * Accessibility Utilities for Zenith Pulse Manager
 * 
 * Comprehensive accessibility utilities for modal/dialog components
 * following WCAG 2.1 AA guidelines and modern accessibility best practices.
 */

// ARIA attributes for different modal types
export const modalAriaAttributes = {
  dialog: {
    role: 'dialog',
    'aria-modal': true,
  },
  alertDialog: {
    role: 'alertdialog',
    'aria-modal': true,
  },
  menu: {
    role: 'menu',
    'aria-modal': false,
  },
  tooltip: {
    role: 'tooltip',
    'aria-modal': false,
  },
} as const;

// Focus management utilities
export class FocusManager {
  private previousActiveElement: Element | null = null;
  private focusableSelectors = [
    'button:not([disabled])',
    '[href]',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ].join(', ');

  /**
   * Store the currently focused element before opening a modal
   */
  storeFocus(): void {
    this.previousActiveElement = document.activeElement;
  }

  /**
   * Restore focus to the previously focused element
   */
  restoreFocus(): void {
    if (this.previousActiveElement && 'focus' in this.previousActiveElement) {
      (this.previousActiveElement as HTMLElement).focus();
    }
  }

  /**
   * Get all focusable elements within a container
   */
  getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(container.querySelectorAll(this.focusableSelectors))
      .filter((element) => this.isElementVisible(element)) as HTMLElement[];
  }

  /**
   * Check if an element is visible and focusable
   */
  private isElementVisible(element: Element): boolean {
    const style = window.getComputedStyle(element);
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      (element as HTMLElement).offsetParent !== null
    );
  }

  /**
   * Focus the first focusable element in a container
   */
  focusFirst(container: HTMLElement): void {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }

  /**
   * Focus the last focusable element in a container
   */
  focusLast(container: HTMLElement): void {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus();
    }
  }

  /**
   * Trap focus within a container
   */
  trapFocus(container: HTMLElement): () => void {
    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);

    // Focus the first element initially
    setTimeout(() => firstElement?.focus(), 100);

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// Keyboard navigation utilities
export const keyboardNavigation = {
  /**
   * Handle escape key to close modals
   */
  handleEscape: (callback: () => void) => (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      callback();
    }
  },

  /**
   * Handle enter key for activation
   */
  handleEnter: (callback: () => void) => (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      callback();
    }
  },

  /**
   * Handle space key for activation
   */
  handleSpace: (callback: () => void) => (event: KeyboardEvent) => {
    if (event.key === ' ') {
      event.preventDefault();
      callback();
    }
  },

  /**
   * Handle arrow keys for navigation
   */
  handleArrowKeys: (
    onUp: () => void,
    onDown: () => void,
    onLeft: () => void,
    onRight: () => void
  ) => (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        onUp();
        break;
      case 'ArrowDown':
        event.preventDefault();
        onDown();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        onLeft();
        break;
      case 'ArrowRight':
        event.preventDefault();
        onRight();
        break;
    }
  },
};

// Screen reader utilities
export const screenReader = {
  /**
   * Announce a message to screen readers
   */
  announce: (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },

  /**
   * Create accessible labels for modal components
   */
  createLabels: (title: string, description?: string) => ({
    'aria-labelledby': 'modal-title',
    'aria-describedby': description ? 'modal-description' : undefined,
  }),

  /**
   * Create accessible button labels
   */
  createButtonLabel: (action: string, context?: string) => {
    return context ? `${action} ${context}` : action;
  },
};

// Z-index management system
export class ZIndexManager {
  private static instance: ZIndexManager;
  private baseZIndex = 1000;
  private currentZIndex = this.baseZIndex;
  private modalStack: string[] = [];

  static getInstance(): ZIndexManager {
    if (!ZIndexManager.instance) {
      ZIndexManager.instance = new ZIndexManager();
    }
    return ZIndexManager.instance;
  }

  /**
   * Get the next available z-index for a modal
   */
  getNextZIndex(modalId: string): number {
    this.modalStack.push(modalId);
    this.currentZIndex += 10;
    return this.currentZIndex;
  }

  /**
   * Release a z-index when a modal is closed
   */
  releaseZIndex(modalId: string): void {
    const index = this.modalStack.indexOf(modalId);
    if (index > -1) {
      this.modalStack.splice(index, 1);
    }

    // Reset to base if no modals are open
    if (this.modalStack.length === 0) {
      this.currentZIndex = this.baseZIndex;
    }
  }

  /**
   * Get the current highest z-index
   */
  getCurrentZIndex(): number {
    return this.currentZIndex;
  }

  /**
   * Check if a modal is the topmost
   */
  isTopmost(modalId: string): boolean {
    return this.modalStack[this.modalStack.length - 1] === modalId;
  }
}

// Body scroll management
export class ScrollManager {
  private static instance: ScrollManager;
  private lockCount = 0;
  private originalOverflow = '';
  private originalPaddingRight = '';

  static getInstance(): ScrollManager {
    if (!ScrollManager.instance) {
      ScrollManager.instance = new ScrollManager();
    }
    return ScrollManager.instance;
  }

  /**
   * Lock body scroll (with reference counting for nested modals)
   */
  lockScroll(): void {
    if (this.lockCount === 0) {
      // Store original values
      this.originalOverflow = document.body.style.overflow;
      this.originalPaddingRight = document.body.style.paddingRight;

      // Calculate scrollbar width to prevent layout shift
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      
      // Apply lock
      document.body.style.overflow = 'hidden';
      if (scrollbarWidth > 0) {
        document.body.style.paddingRight = `${scrollbarWidth}px`;
      }
    }
    this.lockCount++;
  }

  /**
   * Unlock body scroll
   */
  unlockScroll(): void {
    this.lockCount = Math.max(0, this.lockCount - 1);
    
    if (this.lockCount === 0) {
      // Restore original values
      document.body.style.overflow = this.originalOverflow;
      document.body.style.paddingRight = this.originalPaddingRight;
    }
  }

  /**
   * Force unlock all scroll locks (emergency cleanup)
   */
  forceUnlock(): void {
    this.lockCount = 0;
    document.body.style.overflow = this.originalOverflow;
    document.body.style.paddingRight = this.originalPaddingRight;
  }
}

// Utility functions for common accessibility patterns
export const a11yUtils = {
  focusManager: new FocusManager(),
  zIndexManager: ZIndexManager.getInstance(),
  scrollManager: ScrollManager.getInstance(),
  
  /**
   * Setup modal accessibility features
   */
  setupModal: (
    container: HTMLElement,
    modalId: string,
    onClose: () => void
  ) => {
    const focusManager = new FocusManager();
    const zIndexManager = ZIndexManager.getInstance();
    const scrollManager = ScrollManager.getInstance();

    // Store focus and setup focus trap
    focusManager.storeFocus();
    const cleanupFocusTrap = focusManager.trapFocus(container);

    // Get z-index and lock scroll
    const zIndex = zIndexManager.getNextZIndex(modalId);
    container.style.zIndex = zIndex.toString();
    scrollManager.lockScroll();

    // Setup keyboard navigation
    const handleKeyDown = keyboardNavigation.handleEscape(onClose);
    document.addEventListener('keydown', handleKeyDown);

    // Return cleanup function
    return () => {
      cleanupFocusTrap();
      focusManager.restoreFocus();
      zIndexManager.releaseZIndex(modalId);
      scrollManager.unlockScroll();
      document.removeEventListener('keydown', handleKeyDown);
    };
  },

  /**
   * Create accessible modal props
   */
  createModalProps: (
    title: string,
    description?: string,
    type: keyof typeof modalAriaAttributes = 'dialog'
  ) => ({
    ...modalAriaAttributes[type],
    ...screenReader.createLabels(title, description),
  }),
};
