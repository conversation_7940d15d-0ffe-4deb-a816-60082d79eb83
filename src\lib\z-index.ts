/**
 * Z-Index Management System for Zenith Pulse Manager
 * 
 * Centralized z-index values to prevent layering conflicts and ensure
 * proper stacking order across all UI components.
 */

// Base z-index values for different component categories
export const zIndexLayers = {
  // Background and base elements
  background: 0,
  base: 1,
  
  // Content layers
  content: 10,
  elevated: 20,
  
  // Navigation and UI elements
  header: 100,
  navigation: 200,
  sidebar: 300,
  
  // Interactive elements
  dropdown: 400,
  popover: 500,
  tooltip: 600,
  
  // Overlays and modals
  overlay: 1000,
  modal: 1010,
  alertDialog: 1020,
  drawer: 1030,
  sheet: 1040,
  
  // Notifications and feedback
  toast: 2000,
  notification: 2010,
  
  // Critical system elements
  loading: 3000,
  error: 3010,
  debug: 9999,
} as const;

// Component-specific z-index configurations
export const componentZIndex = {
  // Layout components
  mainLayout: zIndexLayers.base,
  contentContainer: zIndexLayers.content,
  pageHeader: zIndexLayers.elevated,
  
  // Navigation components
  mainNavigation: zIndexLayers.navigation,
  breadcrumbs: zIndexLayers.navigation + 1,
  tabNavigation: zIndexLayers.navigation + 2,
  
  // Sidebar components
  sidebar: zIndexLayers.sidebar,
  sidebarOverlay: zIndexLayers.sidebar - 1,
  sidebarContent: zIndexLayers.sidebar + 1,
  
  // Interactive components
  button: zIndexLayers.base,
  input: zIndexLayers.base,
  select: zIndexLayers.dropdown,
  combobox: zIndexLayers.dropdown + 1,
  
  // Overlay components
  popover: zIndexLayers.popover,
  tooltip: zIndexLayers.tooltip,
  hoverCard: zIndexLayers.popover + 1,
  contextMenu: zIndexLayers.dropdown + 2,
  
  // Modal components
  dialogOverlay: zIndexLayers.overlay,
  dialogContent: zIndexLayers.modal,
  alertDialogOverlay: zIndexLayers.overlay + 1,
  alertDialogContent: zIndexLayers.alertDialog,
  
  // Sheet and drawer components
  sheetOverlay: zIndexLayers.overlay + 2,
  sheetContent: zIndexLayers.sheet,
  drawerOverlay: zIndexLayers.overlay + 3,
  drawerContent: zIndexLayers.drawer,
  
  // Notification components
  toast: zIndexLayers.toast,
  sonner: zIndexLayers.toast + 1,
  notification: zIndexLayers.notification,
  
  // Loading and feedback
  loadingSpinner: zIndexLayers.loading,
  progressBar: zIndexLayers.loading + 1,
  
  // AI Assistant (special case - should be above most content but below critical modals)
  aiAssistant: zIndexLayers.modal - 100,
  aiAssistantOverlay: zIndexLayers.overlay - 100,
} as const;

// Utility functions for z-index management
export const zIndexUtils = {
  /**
   * Get z-index value for a component
   */
  get: (component: keyof typeof componentZIndex): number => {
    return componentZIndex[component];
  },

  /**
   * Get CSS z-index string for a component
   */
  getCss: (component: keyof typeof componentZIndex): string => {
    return componentZIndex[component].toString();
  },

  /**
   * Get z-index value with offset
   */
  getWithOffset: (component: keyof typeof componentZIndex, offset: number): number => {
    return componentZIndex[component] + offset;
  },

  /**
   * Check if one component should be above another
   */
  isAbove: (
    component1: keyof typeof componentZIndex,
    component2: keyof typeof componentZIndex
  ): boolean => {
    return componentZIndex[component1] > componentZIndex[component2];
  },

  /**
   * Get the highest z-index in a category
   */
  getHighestInLayer: (layer: keyof typeof zIndexLayers): number => {
    const layerValue = zIndexLayers[layer];
    const componentsInLayer = Object.values(componentZIndex).filter(
      (value) => value >= layerValue && value < layerValue + 100
    );
    return Math.max(...componentsInLayer, layerValue);
  },

  /**
   * Generate Tailwind CSS classes for z-index
   */
  getTailwindClass: (component: keyof typeof componentZIndex): string => {
    const value = componentZIndex[component];
    
    // Map to Tailwind's predefined z-index classes where possible
    const tailwindMap: Record<number, string> = {
      0: 'z-0',
      10: 'z-10',
      20: 'z-20',
      30: 'z-30',
      40: 'z-40',
      50: 'z-50',
    };

    return tailwindMap[value] || `z-[${value}]`;
  },
};

// CSS custom properties for z-index values
export const zIndexCssVars = Object.entries(componentZIndex).reduce(
  (acc, [key, value]) => {
    acc[`--z-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`] = value.toString();
    return acc;
  },
  {} as Record<string, string>
);

// Validation utilities
export const zIndexValidation = {
  /**
   * Validate that modal z-indexes are properly ordered
   */
  validateModalOrder: (): boolean => {
    const modalComponents = [
      'dialogOverlay',
      'dialogContent',
      'alertDialogOverlay',
      'alertDialogContent',
      'sheetOverlay',
      'sheetContent',
    ] as const;

    for (let i = 0; i < modalComponents.length - 1; i++) {
      const current = componentZIndex[modalComponents[i]];
      const next = componentZIndex[modalComponents[i + 1]];
      
      if (current >= next) {
        console.warn(
          `Z-index validation failed: ${modalComponents[i]} (${current}) should be below ${modalComponents[i + 1]} (${next})`
        );
        return false;
      }
    }
    
    return true;
  },

  /**
   * Check for z-index conflicts
   */
  checkConflicts: (): Array<{ components: string[]; zIndex: number }> => {
    const conflicts: Array<{ components: string[]; zIndex: number }> = [];
    const zIndexMap = new Map<number, string[]>();

    // Group components by z-index value
    Object.entries(componentZIndex).forEach(([component, zIndex]) => {
      if (!zIndexMap.has(zIndex)) {
        zIndexMap.set(zIndex, []);
      }
      zIndexMap.get(zIndex)!.push(component);
    });

    // Find conflicts (multiple components with same z-index)
    zIndexMap.forEach((components, zIndex) => {
      if (components.length > 1) {
        conflicts.push({ components, zIndex });
      }
    });

    return conflicts;
  },

  /**
   * Generate a report of all z-index values
   */
  generateReport: (): string => {
    const sortedComponents = Object.entries(componentZIndex)
      .sort(([, a], [, b]) => a - b)
      .map(([component, zIndex]) => `${component}: ${zIndex}`)
      .join('\n');

    const conflicts = zIndexValidation.checkConflicts();
    const conflictReport = conflicts.length > 0
      ? '\n\nConflicts:\n' + conflicts
          .map(({ components, zIndex }) => `Z-index ${zIndex}: ${components.join(', ')}`)
          .join('\n')
      : '\n\nNo conflicts found.';

    return `Z-Index Report:\n\n${sortedComponents}${conflictReport}`;
  },
};

// Development utilities (only available in development mode)
export const devUtils = {
  /**
   * Log z-index information to console
   */
  logZIndexInfo: () => {
    if (process.env.NODE_ENV === 'development') {
      console.group('Z-Index Information');
      console.log(zIndexValidation.generateReport());
      console.groupEnd();
    }
  },

  /**
   * Add visual z-index indicators to elements (development only)
   */
  addVisualIndicators: () => {
    if (process.env.NODE_ENV === 'development') {
      const style = document.createElement('style');
      style.textContent = `
        [data-z-index]::before {
          content: 'z:' attr(data-z-index);
          position: absolute;
          top: 0;
          right: 0;
          background: #ff0000;
          color: white;
          font-size: 10px;
          padding: 2px 4px;
          z-index: 9999;
          pointer-events: none;
        }
      `;
      document.head.appendChild(style);
    }
  },
};

// Export everything for easy access
export default {
  layers: zIndexLayers,
  components: componentZIndex,
  utils: zIndexUtils,
  cssVars: zIndexCssVars,
  validation: zIndexValidation,
  dev: devUtils,
};
