/**
 * Enhanced Dialog Component for Zenith Pulse Manager
 * 
 * An improved version of the shadcn-ui Dialog component with better responsive behavior,
 * accessibility features, and cross-device compatibility.
 */

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  getModalOverlayClasses,
  getModalContentClasses,
  getModalHeaderClasses,
  getModalBodyClasses,
  getModalFooterClasses,
  getModalA11yProps,
  modalSizes
} from "@/lib/modal-utils"
import { a11yUtils } from "@/lib/accessibility-utils"
import { zIndexUtils } from "@/lib/z-index"

const EnhancedDialog = DialogPrimitive.Root

const EnhancedDialogTrigger = DialogPrimitive.Trigger

const EnhancedDialogPortal = DialogPrimitive.Portal

const EnhancedDialogClose = DialogPrimitive.Close

const EnhancedDialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 bg-black/80 backdrop-blur-sm",
      zIndexUtils.getTailwindClass('dialogOverlay'),
      "data-[state=open]:animate-in data-[state=closed]:animate-out",
      "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
EnhancedDialogOverlay.displayName = DialogPrimitive.Overlay.displayName

interface EnhancedDialogContentProps 
  extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> {
  size?: keyof typeof modalSizes
  showCloseButton?: boolean
}

const EnhancedDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  EnhancedDialogContentProps
>(({ className, children, size = 'md', showCloseButton = true, ...props }, ref) => {
  const contentRef = React.useRef<HTMLDivElement>(null)
  const modalId = React.useId()

  React.useEffect(() => {
    const content = contentRef.current
    if (!content) return

    // Setup accessibility features
    const cleanup = a11yUtils.setupModal(content, modalId, () => {
      // Modal close handler would be passed from parent
    })

    return cleanup
  }, [modalId])

  return (
    <EnhancedDialogPortal>
      <EnhancedDialogOverlay />
      <DialogPrimitive.Content
        ref={ref}
        className={cn(
          getModalContentClasses(size, 'md'),
          zIndexUtils.getTailwindClass('dialogContent'),
          className
        )}
        {...props}
        {...a11yUtils.createModalProps('Dialog')}
      >
        <div ref={contentRef} className="flex flex-col h-full">
          {children}
          {showCloseButton && (
            <DialogPrimitive.Close className={cn(
              "absolute right-3 top-3 sm:right-4 sm:top-4",
              "rounded-sm opacity-70 ring-offset-background transition-opacity",
              "hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
              "disabled:pointer-events-none",
              "h-7 w-7 p-0 sm:h-8 sm:w-8",
              "flex items-center justify-center",
              "touch-manipulation",
              "data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            )}>
              <X className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>
          )}
        </div>
      </DialogPrimitive.Content>
    </EnhancedDialogPortal>
  )
})
EnhancedDialogContent.displayName = DialogPrimitive.Content.displayName

const EnhancedDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={getModalHeaderClasses(cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      "pr-8 sm:pr-10", // Space for close button
      className
    ))}
    {...props}
  />
)
EnhancedDialogHeader.displayName = "EnhancedDialogHeader"

const EnhancedDialogBody = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={getModalBodyClasses(className)}
    {...props}
  />
)
EnhancedDialogBody.displayName = "EnhancedDialogBody"

const EnhancedDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={getModalFooterClasses(className)}
    {...props}
  />
)
EnhancedDialogFooter.displayName = "EnhancedDialogFooter"

const EnhancedDialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    id="modal-title"
    className={cn(
      "font-semibold leading-none tracking-tight",
      "text-base sm:text-lg md:text-xl",
      className
    )}
    {...props}
  />
))
EnhancedDialogTitle.displayName = DialogPrimitive.Title.displayName

const EnhancedDialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    id="modal-description"
    className={cn(
      "text-muted-foreground",
      "text-xs sm:text-sm",
      className
    )}
    {...props}
  />
))
EnhancedDialogDescription.displayName = DialogPrimitive.Description.displayName

// Convenience component that combines common dialog patterns
interface ResponsiveDialogProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
  footer?: React.ReactNode
  size?: keyof typeof modalSizes
  showCloseButton?: boolean
  className?: string
}

const ResponsiveDialog: React.FC<ResponsiveDialogProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  size = 'md',
  showCloseButton = true,
  className
}) => {
  return (
    <EnhancedDialog open={isOpen} onOpenChange={onClose}>
      <EnhancedDialogContent 
        size={size} 
        showCloseButton={showCloseButton}
        className={className}
      >
        <EnhancedDialogHeader>
          <EnhancedDialogTitle>{title}</EnhancedDialogTitle>
          {description && (
            <EnhancedDialogDescription>{description}</EnhancedDialogDescription>
          )}
        </EnhancedDialogHeader>
        
        <EnhancedDialogBody>
          {children}
        </EnhancedDialogBody>
        
        {footer && (
          <EnhancedDialogFooter>
            {footer}
          </EnhancedDialogFooter>
        )}
      </EnhancedDialogContent>
    </EnhancedDialog>
  )
}

export {
  EnhancedDialog,
  EnhancedDialogPortal,
  EnhancedDialogOverlay,
  EnhancedDialogClose,
  EnhancedDialogTrigger,
  EnhancedDialogContent,
  EnhancedDialogHeader,
  EnhancedDialogBody,
  EnhancedDialogFooter,
  EnhancedDialogTitle,
  EnhancedDialogDescription,
  ResponsiveDialog,
}
